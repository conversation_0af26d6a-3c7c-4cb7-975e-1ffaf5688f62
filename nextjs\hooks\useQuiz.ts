import { useState } from 'react';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import quizApi from '@/lib/apis/quizApi';
import type { QuizData, QuizResponse } from '@/types/quizType';

// Query keys
export const quizKeys = {
  all: ['quiz'] as const,
  submissions: () => [...quizKeys.all, 'submissions'] as const,
  submission: (id: string) => [...quizKeys.submissions(), id] as const,
};

/**
 * 提交问卷 hook，包含dialog状态管理和PDF下载功能
 */
export const useSubmitQuiz = () => {
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pdfResult, setPdfResult] = useState<QuizResponse | null>(null);

  const mutation = useMutation({
    mutationKey: quizKeys.submissions(),
    mutationFn: async (data: QuizData) => {
      // 打开dialog显示等待状态
      setIsDialogOpen(true);
      setPdfResult(null);

      const result = await quizApi.submitQuiz(data);
      return result;
    },
    onSuccess: result => {
      if (result.success) {
        setPdfResult(result);
        // 提交成功后刷新相关查询
        queryClient.invalidateQueries({ queryKey: quizKeys.submissions() });
        toast.success('问卷提交成功！');
      } else {
        toast.error(result.message || '提交失败');
        setIsDialogOpen(false);
      }
    },
    onError: error => {
      toast.error('提交失败');
      console.error('提交失败:', error);
      setIsDialogOpen(false);
    },
  });

  // 下载PDF文件
  const downloadPdf = () => {
    if (!pdfResult) return;

    if (pdfResult.pdfData) {
      // 如果有PDF二进制数据，直接下载
      const url = URL.createObjectURL(pdfResult.pdfData);
      const link = document.createElement('a');
      link.href = url;
      link.download = `career-guidance-report-${new Date().getTime()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  // 关闭dialog
  const closeDialog = () => {
    setIsDialogOpen(false);
    setPdfResult(null);
  };

  return {
    ...mutation,
    isDialogOpen,
    pdfResult,
    downloadPdf,
    closeDialog,
  };
};
